"""
مثال على كيفية استخدام الفئة الأساسية BaseTableWidget
هذا المثال يوضح كيفية إنشاء قسم جديد باستخدام الفئة الموحدة
"""

from PyQt5.QtWidgets import QMessageBox
from ui.base_table import BaseTableWidget
from database import Client  # مثال: استخدام نموذج العملاء

class ExampleClientsWidget(BaseTableWidget):
    """مثال على استخدام الفئة الأساسية لإنشاء قسم العملاء"""
    
    def __init__(self, session):
        # تهيئة الفئة الأساسية مع اسم القسم والأيقونة
        super().__init__(session, "العملاء", "🤝")
        
        # تكوين الأعمدة للعملاء
        self.columns_config = [
            {
                'header': '🔢 ID',
                'field': 'id',
                'icon': '🔢',
                'width': 100,
                'type': 'id'
            },
            {
                'header': '👤 اسم العميل',
                'field': 'name',
                'icon': '👤',
                'width': 300,
                'type': 'text'
            },
            {
                'header': '🏠 العنوان',
                'field': 'address',
                'icon': '🏠',
                'width': 300,
                'type': 'text'
            },
            {
                'header': '📧 البريد الإلكتروني',
                'field': 'email',
                'icon': '📧',
                'width': 240,
                'type': 'email'
            },
            {
                'header': '📱 رقم الهاتف',
                'field': 'phone',
                'icon': '📱',
                'width': 170,
                'type': 'phone'
            },
            {
                'header': '💵 الرصيد',
                'field': 'balance',
                'icon': '💵',
                'width': 160,
                'type': 'currency'
            },
            {
                'header': '⭐ حالة العميل',
                'field': 'status',
                'icon': '⭐',
                'width': 170,
                'type': 'status'
            },
            {
                'header': '📋 الملاحظات',
                'field': 'notes',
                'icon': '📋',
                'width': 280,
                'type': 'text'
            },
            {
                'header': '🗓️ تاريخ الإضافة',
                'field': 'created_at',
                'icon': '🗓️',
                'width': 180,
                'type': 'date'
            }
        ]
        
        # إنشاء التخطيط الموحد
        self.create_unified_layout(self.columns_config)
        
        # تحميل البيانات
        self.load_data()
        
    def load_data(self):
        """تحميل بيانات العملاء"""
        try:
            # جلب جميع العملاء من قاعدة البيانات
            clients = self.session.query(Client).order_by(Client.id.asc()).all()
            
            # ملء الجدول بالبيانات
            self.populate_unified_table(clients, self.columns_config)
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات العملاء: {str(e)}")
            
    def get_cell_data(self, client, config):
        """تخصيص عرض البيانات للعملاء"""
        try:
            field_name = config.get('field', '')
            icon = config.get('icon', '')
            data_type = config.get('type', 'text')
            
            # معالجة خاصة لحقل ID مع أيقونة ديناميكية
            if field_name == 'id':
                balance_value = getattr(client, 'balance', 0) or 0
                if balance_value > 0:
                    dynamic_icon = "💰"  # دائن
                elif balance_value < 0:
                    dynamic_icon = "🔴"  # مدين
                else:
                    dynamic_icon = "🔢"  # متوازن
                return f"{dynamic_icon} {client.id}"
                
            # معالجة خاصة لحقل الحالة
            elif field_name == 'status':
                balance = getattr(client, 'balance', 0) or 0
                if balance > 0:
                    return "🟢 نشط"
                elif balance == 0:
                    return "🟡 عادي"
                else:
                    return "🔴 مدين"
                    
            # استخدام الطريقة الأساسية للحقول الأخرى
            else:
                return super().get_cell_data(client, config)
                
        except Exception as e:
            return "No data"
            
    # تنفيذ المعالجات المطلوبة
    def on_add_clicked(self):
        """معالج زر إضافة عميل"""
        QMessageBox.information(self, "إضافة عميل", "سيتم إضافة نافذة إضافة عميل جديد قريباً")
        
    def on_edit_clicked(self):
        """معالج زر تعديل عميل"""
        selected_id = self.get_selected_item_id()
        if selected_id:
            QMessageBox.information(self, "تعديل عميل", f"سيتم إضافة نافذة تعديل العميل رقم {selected_id} قريباً")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للتعديل")
            
    def on_delete_clicked(self):
        """معالج زر حذف عميل"""
        selected_id = self.get_selected_item_id()
        if selected_id:
            reply = QMessageBox.question(
                self, "تأكيد الحذف", 
                f"هل أنت متأكد من حذف العميل رقم {selected_id}؟",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                try:
                    client = self.session.query(Client).filter_by(id=selected_id).first()
                    if client:
                        self.session.delete(client)
                        self.session.commit()
                        self.load_data()  # إعادة تحميل البيانات
                        QMessageBox.information(self, "نجح", "تم حذف العميل بنجاح")
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"فشل في حذف العميل: {str(e)}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للحذف")
            
    def on_view_clicked(self):
        """معالج زر عرض تفاصيل العميل"""
        selected_id = self.get_selected_item_id()
        if selected_id:
            try:
                client = self.session.query(Client).filter_by(id=selected_id).first()
                if client:
                    details = f"""
                    تفاصيل العميل:
                    
                    الرقم: {client.id}
                    الاسم: {client.name or 'غير محدد'}
                    العنوان: {client.address or 'غير محدد'}
                    البريد الإلكتروني: {client.email or 'غير محدد'}
                    رقم الهاتف: {client.phone or 'غير محدد'}
                    الرصيد: {client.balance or 0} جنيه
                    """
                    QMessageBox.information(self, "تفاصيل العميل", details)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في عرض تفاصيل العميل: {str(e)}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل لعرض التفاصيل")
            
    def on_statistics_clicked(self):
        """معالج زر الإحصائيات"""
        try:
            total_clients = self.session.query(Client).count()
            active_clients = self.session.query(Client).filter(Client.balance > 0).count()
            debtor_clients = self.session.query(Client).filter(Client.balance < 0).count()
            
            stats = f"""
            إحصائيات العملاء:
            
            إجمالي العملاء: {total_clients}
            العملاء النشطون: {active_clients}
            العملاء المدينون: {debtor_clients}
            العملاء العاديون: {total_clients - active_clients - debtor_clients}
            """
            QMessageBox.information(self, "إحصائيات العملاء", stats)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في عرض الإحصائيات: {str(e)}")
            
    def on_search_changed(self):
        """معالج تغيير نص البحث"""
        self.filter_data()
        
    def on_search_clicked(self):
        """معالج النقر على زر البحث"""
        self.filter_data()
        
    def filter_data(self):
        """تصفية البيانات حسب نص البحث"""
        try:
            search_text = self.search_edit.text().strip().lower()
            
            # بناء الاستعلام
            query = self.session.query(Client)
            
            if search_text:
                query = query.filter(
                    Client.name.ilike(f'%{search_text}%') |
                    Client.address.ilike(f'%{search_text}%') |
                    Client.email.ilike(f'%{search_text}%') |
                    Client.phone.ilike(f'%{search_text}%')
                )
                
            # تنفيذ الاستعلام
            clients = query.order_by(Client.id.asc()).all()
            
            # تحديث الجدول
            self.populate_unified_table(clients, self.columns_config)
            
        except Exception as e:
            print(f"خطأ في تصفية البيانات: {str(e)}")
            
    def refresh_unified_table(self):
        """تحديث الجدول"""
        self.load_data()
        
    # معالجات التصدير
    def on_export_excel(self):
        """تصدير إلى Excel"""
        QMessageBox.information(self, "تصدير Excel", "سيتم إضافة وظيفة تصدير Excel قريباً")
        
    def on_export_csv(self):
        """تصدير إلى CSV"""
        QMessageBox.information(self, "تصدير CSV", "سيتم إضافة وظيفة تصدير CSV قريباً")
        
    def on_export_pdf(self):
        """تصدير إلى PDF"""
        QMessageBox.information(self, "تصدير PDF", "سيتم إضافة وظيفة تصدير PDF قريباً")


# مثال على كيفية الاستخدام:
"""
# في الملف الرئيسي للقسم:
from ui.example_usage import ExampleClientsWidget

class ClientsWidget(ExampleClientsWidget):
    def __init__(self, session):
        super().__init__(session)
        # يمكن إضافة تخصيصات إضافية هنا
"""
