# دليل استخدام الفئة الأساسية BaseTableWidget

## نظرة عامة
تم إنشاء فئة `BaseTableWidget` كفئة أساسية موحدة لجميع الجداول في البرنامج، مما يضمن التناسق والتوحيد بنسبة 100% عبر جميع الأقسام.

## المميزات الرئيسية

### ✅ **التصميم الموحد 100%**
- تصميم جدول متطور مع ألوان وتدرجات موحدة
- عناوين أعمدة بتصميم احترافي مع تأثيرات hover
- خلايا بتصميم متدرج مع حدود وظلال
- تأثيرات تفاعلية للتحديد والتمرير

### ✅ **العلامة المائية الموحدة**
- علامة مائية "Smart Finish" بحجم 180px
- شفافية 8% لعدم التداخل مع البيانات
- موضعة في منتصف الجدول

### ✅ **نظام الأزرار الموحد**
- 7 أزرار أساسية: إضافة، تعديل، حذف، تحديث، عرض، تصدير، إحصائيات
- ألوان مختلفة لكل زر حسب الوظيفة
- تأثيرات hover وpress موحدة
- قائمة تصدير منسدلة (Excel, CSV, PDF)

### ✅ **نظام البحث والتصفية الموحد**
- حقل بحث مع تصميم متطور
- قائمة تصفية قابلة للتخصيص
- تأثيرات تفاعلية موحدة

### ✅ **نظام الأيقونات المتطور**
- أيقونات موحدة لجميع أنواع البيانات
- أيقونات ديناميكية حسب حالة البيانات
- عرض "No data" باللون الأحمر للحقول الفارغة

## كيفية الاستخدام

### 1. الاستيراد والوراثة
```python
from ui.base_table import BaseTableWidget

class YourSectionWidget(BaseTableWidget):
    def __init__(self, session):
        # تهيئة الفئة الأساسية
        super().__init__(session, "اسم القسم", "🔥")
```

### 2. تكوين الأعمدة
```python
self.columns_config = [
    {
        'header': '🔢 ID',           # عنوان العمود مع الأيقونة
        'field': 'id',              # اسم الحقل في قاعدة البيانات
        'icon': '🔢',               # الأيقونة للبيانات
        'width': 100,               # عرض العمود
        'type': 'id'                # نوع البيانات
    },
    {
        'header': '👤 الاسم',
        'field': 'name',
        'icon': '👤',
        'width': 300,
        'type': 'text'
    },
    {
        'header': '💵 المبلغ',
        'field': 'amount',
        'icon': '💵',
        'width': 160,
        'type': 'currency'          # سيتم تنسيقه كعملة
    },
    {
        'header': '🗓️ التاريخ',
        'field': 'created_at',
        'icon': '🗓️',
        'width': 180,
        'type': 'date'              # سيتم تنسيقه كتاريخ
    }
]
```

### 3. إنشاء التخطيط
```python
# إنشاء التخطيط الموحد
self.create_unified_layout(self.columns_config)

# تحميل البيانات
self.load_data()
```

### 4. تنفيذ الوظائف المطلوبة
```python
def load_data(self):
    """تحميل البيانات من قاعدة البيانات"""
    try:
        data = self.session.query(YourModel).all()
        self.populate_unified_table(data, self.columns_config)
    except Exception as e:
        print(f"خطأ في تحميل البيانات: {str(e)}")

def on_add_clicked(self):
    """معالج زر الإضافة"""
    # تنفيذ منطق الإضافة
    pass

def on_edit_clicked(self):
    """معالج زر التعديل"""
    selected_id = self.get_selected_item_id()
    if selected_id:
        # تنفيذ منطق التعديل
        pass

def on_delete_clicked(self):
    """معالج زر الحذف"""
    selected_id = self.get_selected_item_id()
    if selected_id:
        # تنفيذ منطق الحذف
        pass
```

## أنواع البيانات المدعومة

### النوع `text`
- عرض نص عادي مع أيقونة
- عرض "No data" للحقول الفارغة

### النوع `currency`
- تنسيق المبلغ بالجنيه
- استخدام دالة `format_currency`

### النوع `date`
- تنسيق التاريخ بصيغة YYYY-MM-DD
- معالجة كائنات datetime

### النوع `id`
- عرض الرقم التسلسلي
- يمكن تخصيص الأيقونة ديناميكياً

### النوع `status`
- عرض حالة مخصصة
- يمكن ربطها بحقول أخرى

## التخصيص المتقدم

### تخصيص عرض البيانات
```python
def get_cell_data(self, item, config):
    """تخصيص عرض البيانات"""
    field_name = config.get('field', '')
    
    # معالجة خاصة لحقل معين
    if field_name == 'status':
        balance = getattr(item, 'balance', 0) or 0
        if balance > 0:
            return "🟢 نشط"
        elif balance == 0:
            return "🟡 عادي"
        else:
            return "🔴 مدين"
    
    # استخدام الطريقة الأساسية للحقول الأخرى
    return super().get_cell_data(item, config)
```

### تخصيص البحث والتصفية
```python
def filter_data(self):
    """تصفية البيانات حسب نص البحث"""
    search_text = self.search_edit.text().strip().lower()
    
    query = self.session.query(YourModel)
    
    if search_text:
        query = query.filter(
            YourModel.name.ilike(f'%{search_text}%') |
            YourModel.description.ilike(f'%{search_text}%')
        )
    
    data = query.all()
    self.populate_unified_table(data, self.columns_config)
```

## الإشارات المتاحة

### `item_selected(int)`
- تُرسل عند تحديد عنصر في الجدول
- تحتوي على معرف العنصر المحدد

### `item_double_clicked(int)`
- تُرسل عند النقر المزدوج على عنصر
- تحتوي على معرف العنصر

### `refresh_requested()`
- تُرسل عند النقر على زر التحديث

## الوظائف المساعدة

### `get_selected_item_id()`
- إرجاع معرف العنصر المحدد حالياً
- إرجاع None إذا لم يكن هناك تحديد

### `clear_unified_table()`
- مسح جميع بيانات الجدول

### `populate_unified_table(data, columns_config)`
- ملء الجدول بالبيانات حسب التكوين المحدد

## مثال كامل
راجع ملف `ui/example_usage.py` للحصول على مثال كامل لاستخدام الفئة الأساسية.

## الفوائد

### ✅ **توفير الوقت**
- لا حاجة لإعادة كتابة كود الجدول لكل قسم
- تطبيق التصميم الموحد تلقائياً

### ✅ **سهولة الصيانة**
- تعديل واحد في الفئة الأساسية يؤثر على جميع الأقسام
- تقليل تكرار الكود

### ✅ **التناسق المضمون**
- جميع الجداول تبدو وتتصرف بنفس الطريقة
- تجربة مستخدم موحدة

### ✅ **قابلية التوسع**
- إضافة ميزات جديدة للفئة الأساسية تنعكس على جميع الأقسام
- سهولة إضافة أقسام جديدة

## ملاحظات مهمة

1. **يجب تنفيذ الوظائف المطلوبة** في الفئة المشتقة
2. **تكوين الأعمدة ضروري** لعمل الجدول بشكل صحيح
3. **استخدام الأيقونات المناسبة** لكل نوع بيانات
4. **معالجة الأخطاء** في جميع الوظائف المخصصة

---

**الآن يمكن نسخ تصميم جدول العملاء على جميع الأقسام بنسبة 100% مع الحفاظ على التخصيص المطلوب لكل قسم!**
